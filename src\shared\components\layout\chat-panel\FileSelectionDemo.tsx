import React, { useState } from 'react';
import { Button, Typography, Card } from '@/shared/components/common';
import FileSelectionModal from './FileSelectionModal';

/**
 * Demo component để test FileSelectionModal
 */
const FileSelectionDemo: React.FC = () => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedFiles, setSelectedFiles] = useState<Array<{ id: string; name: string; type: 'knowledge' | 'media'; url?: string }>>([]);

  const handleSelectFiles = (files: Array<{ id: string; name: string; type: 'knowledge' | 'media'; url?: string }>) => {
    setSelectedFiles(files);
    console.log('Selected files:', files);
  };

  return (
    <div className="p-6 max-w-4xl mx-auto">
      <Typography variant="h2" className="mb-6">
        File Selection Modal Demo
      </Typography>

      <Card className="p-6 mb-6">
        <Typography variant="h4" className="mb-4">
          Test FileSelectionModal
        </Typography>
        
        <Button 
          variant="primary" 
          onClick={() => setIsModalOpen(true)}
          className="mb-4"
        >
          Mở Modal Chọn File
        </Button>

        {selectedFiles.length > 0 && (
          <div className="mt-4">
            <Typography variant="h5" className="mb-2">
              File đã chọn ({selectedFiles.length}):
            </Typography>
            <div className="space-y-2">
              {selectedFiles.map((file) => (
                <div key={file.id} className="flex items-center gap-3 p-2 bg-gray-50 dark:bg-gray-800 rounded">
                  <span className={`px-2 py-1 rounded text-xs text-white ${
                    file.type === 'knowledge' ? 'bg-blue-500' : 'bg-green-500'
                  }`}>
                    {file.type === 'knowledge' ? 'Tri thức' : 'Media'}
                  </span>
                  <Typography variant="body2">{file.name}</Typography>
                  <Typography variant="caption" className="text-gray-500">
                    ID: {file.id}
                  </Typography>
                </div>
              ))}
            </div>
          </div>
        )}
      </Card>

      <FileSelectionModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        onSelectFiles={handleSelectFiles}
      />
    </div>
  );
};

export default FileSelectionDemo;
