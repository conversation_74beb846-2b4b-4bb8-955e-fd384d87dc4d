import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { Modal, Button, Typography, Input, Checkbox, Icon, Card } from '@/shared/components/common';
import { useKnowledgeFiles } from '@/modules/data/knowledge-files/hooks/useKnowledgeFileQuery';
import { useMediaList } from '@/modules/data/media/hooks/useMediaQuery';
import { FileResponseDto as KnowledgeFileDto } from '@/modules/data/knowledge-files/types/knowledge-files.types';
import { MediaDto } from '@/modules/data/media/types/media.types';

interface FileSelectionModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSelectFiles: (files: Array<{ id: string; name: string; type: 'knowledge' | 'media'; url?: string }>) => void;
}

type FileItem = {
  id: string;
  name: string;
  type: 'knowledge' | 'media';
  size: number;
  extension?: string;
  url?: string;
  createdAt: number;
};

const FileSelectionModal: React.FC<FileSelectionModalProps> = ({
  isOpen,
  onClose,
  onSelectFiles,
}) => {
  const { t } = useTranslation();
  const [activeTab, setActiveTab] = useState<'knowledge' | 'media'>('knowledge');
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedFiles, setSelectedFiles] = useState<string[]>([]);
  const [currentPage, setCurrentPage] = useState(1);
  const pageSize = 10;

  // Query cho file tri thức
  const {
    data: knowledgeFilesData,
    isLoading: isLoadingKnowledge,
  } = useKnowledgeFiles({
    page: currentPage,
    limit: pageSize,
    search: searchTerm || undefined,
  });

  // Query cho file media
  const {
    data: mediaFilesData,
    isLoading: isLoadingMediaFiles,
  } = useMediaList({
    page: currentPage,
    limit: pageSize,
    search: searchTerm || undefined,
  });

  // Reset khi đóng modal
  useEffect(() => {
    if (!isOpen) {
      setSelectedFiles([]);
      setSearchTerm('');
      setCurrentPage(1);
      setActiveTab('knowledge');
    }
  }, [isOpen]);

  // Chuyển đổi dữ liệu về format chung
  const getFileItems = (): FileItem[] => {
    if (activeTab === 'knowledge') {
      return (knowledgeFilesData?.items || []).map((file: KnowledgeFileDto) => ({
        id: file.id,
        name: file.name,
        type: 'knowledge' as const,
        size: file.storage,
        extension: file.extension,
        url: file.viewUrl,
        createdAt: file.createdAt,
      }));
    } else {
      return (mediaFilesData?.items || []).map((file: MediaDto) => ({
        id: file.id,
        name: file.name,
        type: 'media' as const,
        size: file.size,
        url: file.viewUrl,
        createdAt: file.createdAt,
      }));
    }
  };

  const fileItems = getFileItems();
  const isLoading = activeTab === 'knowledge' ? isLoadingKnowledge : isLoadingMediaFiles;
  const totalItems = activeTab === 'knowledge'
    ? knowledgeFilesData?.meta?.totalItems || 0
    : mediaFilesData?.meta?.totalItems || 0;

  // Xử lý chọn/bỏ chọn file
  const handleFileSelect = (fileId: string, checked: boolean) => {
    if (checked) {
      setSelectedFiles(prev => [...prev, fileId]);
    } else {
      setSelectedFiles(prev => prev.filter(id => id !== fileId));
    }
  };

  // Xử lý chọn tất cả
  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedFiles(fileItems.map(file => file.id));
    } else {
      setSelectedFiles([]);
    }
  };

  // Xử lý xác nhận chọn file
  const handleConfirm = () => {
    const selectedFileItems = fileItems
      .filter(file => selectedFiles.includes(file.id))
      .map(file => ({
        id: file.id,
        name: file.name,
        type: file.type,
        url: file.url,
      }));
    
    onSelectFiles(selectedFileItems);
    onClose();
  };

  // Lấy icon cho file
  const getFileIcon = (fileName: string): string => {
    const extension = fileName.split('.').pop()?.toLowerCase();
    switch (extension) {
      case 'pdf':
        return 'file-pdf';
      case 'doc':
      case 'docx':
        return 'file-word';
      case 'xls':
      case 'xlsx':
        return 'file-excel';
      case 'jpg':
      case 'jpeg':
      case 'png':
      case 'gif':
      case 'webp':
        return 'image';
      case 'mp4':
      case 'avi':
      case 'mov':
        return 'video';
      case 'mp3':
      case 'wav':
        return 'music';
      default:
        return 'file';
    }
  };

  // Format kích thước file
  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title="Chọn từ kho của bạn"
      size="xl"
      footer={
        <div className="flex justify-between items-center">
          <Typography variant="body2" className="text-gray-600">
            Đã chọn {selectedFiles.length} file
          </Typography>
          <div className="flex gap-2">
            <Button variant="outline" onClick={onClose}>
              {t('common.cancel', 'Hủy')}
            </Button>
            <Button 
              variant="primary" 
              onClick={handleConfirm}
              disabled={selectedFiles.length === 0}
            >
              {t('common.confirm', 'Xác nhận')} ({selectedFiles.length})
            </Button>
          </div>
        </div>
      }
    >
      <div className="space-y-4">
        {/* Tab Navigation */}
        <div className="flex border-b border-gray-200 dark:border-gray-700">
          <button
            className={`px-4 py-2 font-medium text-sm border-b-2 transition-colors ${
              activeTab === 'knowledge'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700'
            }`}
            onClick={() => {
              setActiveTab('knowledge');
              setCurrentPage(1);
              setSelectedFiles([]);
            }}
          >
            File tri thức
          </button>
          <button
            className={`px-4 py-2 font-medium text-sm border-b-2 transition-colors ${
              activeTab === 'media'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700'
            }`}
            onClick={() => {
              setActiveTab('media');
              setCurrentPage(1);
              setSelectedFiles([]);
            }}
          >
            Ảnh & Media
          </button>
        </div>

        {/* Search */}
        <div className="relative">
          <Input
            placeholder={`Tìm kiếm ${activeTab === 'knowledge' ? 'file tri thức' : 'ảnh & media'}...`}
            value={searchTerm}
            onChange={(e) => {
              setSearchTerm(e.target.value);
              setCurrentPage(1);
            }}
            className="pl-10"
          />
          <Icon 
            name="search" 
            size="sm" 
            className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"
          />
        </div>

        {/* Select All */}
        {fileItems.length > 0 && (
          <div className="flex items-center gap-2">
            <Checkbox
              checked={selectedFiles.length === fileItems.length && fileItems.length > 0}
              indeterminate={selectedFiles.length > 0 && selectedFiles.length < fileItems.length}
              onChange={handleSelectAll}
            />
            <Typography variant="body2">
              Chọn tất cả ({fileItems.length} file)
            </Typography>
          </div>
        )}

        {/* File List */}
        <div className="max-h-96 overflow-y-auto space-y-2">
          {isLoading ? (
            <div className="flex justify-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            </div>
          ) : fileItems.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              {searchTerm ? 'Không tìm thấy file nào' : 'Chưa có file nào'}
            </div>
          ) : (
            fileItems.map((file) => (
              <Card key={file.id} className="p-3 hover:bg-gray-50 dark:hover:bg-gray-800">
                <div className="flex items-center gap-3">
                  <Checkbox
                    checked={selectedFiles.includes(file.id)}
                    onChange={(checked) => handleFileSelect(file.id, checked)}
                  />
                  <div className="flex-shrink-0">
                    <Icon
                      name={getFileIcon(file.name)}
                      size="md"
                      className="text-blue-500"
                    />
                  </div>
                  <div className="flex-1 min-w-0">
                    <Typography variant="body2" className="font-medium truncate">
                      {file.name}
                    </Typography>
                    <Typography variant="caption" className="text-gray-500">
                      {formatFileSize(file.size)} • {new Date(file.createdAt * 1000).toLocaleDateString('vi-VN')}
                    </Typography>
                  </div>
                </div>
              </Card>
            ))
          )}
        </div>

        {/* Pagination */}
        {totalItems > pageSize && (
          <div className="flex justify-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
              disabled={currentPage === 1}
            >
              Trước
            </Button>
            <Typography variant="body2" className="flex items-center px-3">
              Trang {currentPage} / {Math.ceil(totalItems / pageSize)}
            </Typography>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setCurrentPage(prev => prev + 1)}
              disabled={currentPage >= Math.ceil(totalItems / pageSize)}
            >
              Sau
            </Button>
          </div>
        )}
      </div>
    </Modal>
  );
};

export default FileSelectionModal;
